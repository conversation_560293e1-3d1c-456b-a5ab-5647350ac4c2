import { Transaction, ITransaction } from './transaction.model';

interface PaginationQuery {
  page?: string;
  limit?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  role?: string;
}

interface PaginatedResult<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

const getMyTransactions = async (userId: string, query: PaginationQuery = {}): Promise<PaginatedResult<ITransaction>> => {
  // Extract pagination parameters with defaults
  const page = parseInt(query.page || '1', 10);
  const limit = parseInt(query.limit || '10', 10);
  const sortBy = query.sortBy || 'createdAt';
  const sortOrder = query.sortOrder === 'asc' ? 1 : -1;
  const role = query.role as string;

  // Validate pagination parameters
  const currentPage = Math.max(1, page);
  const itemsPerPage = Math.min(Math.max(1, limit), 100); // Max 100 items per page
  const skip = (currentPage - 1) * itemsPerPage;

  // Build sort object
  const sortObject: Record<string, 1 | -1> = {};
  sortObject[sortBy] = sortOrder;

  // Build aggregation pipeline for role filtering
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const pipeline: any[] = [
    // Match user's transactions first
    {
      $match: {
        $or: [{ sender: userId }, { receiver: userId }]
      }
    },

    // Lookup sender data
    {
      $lookup: {
        from: 'users',
        localField: 'sender',
        foreignField: '_id',
        as: 'senderData'
      }
    },

    // Lookup receiver data
    {
      $lookup: {
        from: 'users',
        localField: 'receiver',
        foreignField: '_id',
        as: 'receiverData'
      }
    },

    // Filter by role if specified (transactions where the OTHER party has the specified role)
    ...(role ? [{
      $match: {
        $or: [
          // If current user is sender, check receiver's role
          {
            $and: [
              { sender: { $eq: userId } },
              { 'receiverData.role': role }
            ]
          },
          // If current user is receiver, check sender's role
          {
            $and: [
              { receiver: { $eq: userId } },
              { 'senderData.role': role }
            ]
          }
        ]
      }
    }] : []),

    // Project the final structure
    {
      $project: {
        sender: {
          $cond: {
            if: { $gt: [{ $size: '$senderData' }, 0] },
            then: {
              _id: { $arrayElemAt: ['$senderData._id', 0] },
              name: { $arrayElemAt: ['$senderData.name', 0] },
              email: { $arrayElemAt: ['$senderData.email', 0] }
            },
            else: null
          }
        },
        receiver: {
          $cond: {
            if: { $gt: [{ $size: '$receiverData' }, 0] },
            then: {
              _id: { $arrayElemAt: ['$receiverData._id', 0] },
              name: { $arrayElemAt: ['$receiverData.name', 0] },
              email: { $arrayElemAt: ['$receiverData.email', 0] }
            },
            else: null
          }
        },
        amount: 1,
        type: 1,
        status: 1,
        fee: 1,
        commission: 1,
        description: 1,
        createdAt: 1,
        updatedAt: 1
      }
    }
  ];

  // Get total count with the same filters
  const countPipeline = [...pipeline, { $count: 'total' }];
  const countResult = await Transaction.aggregate(countPipeline);
  const totalItems = countResult.length > 0 ? countResult[0].total : 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Add sorting, skip, and limit to the main pipeline
  pipeline.push(
    { $sort: sortObject },
    { $skip: skip },
    { $limit: itemsPerPage }
  );

  const transactions = await Transaction.aggregate(pipeline);

  return {
    data: transactions,
    pagination: {
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    },
  };
};

const getAllTransactions = async (query: Record<string, unknown>): Promise<PaginatedResult<ITransaction>> => {
  // Extract pagination parameters from query
  const page = parseInt((query.page as string) || '1', 10);
  const limit = parseInt((query.limit as string) || '10', 10);
  const sortBy = (query.sortBy as string) || 'createdAt';
  const sortOrder = (query.sortOrder as string) === 'asc' ? 1 : -1;
  const role = query.role as string;

  // Remove pagination and role parameters from the main query
  const filterQuery = { ...query };
  delete filterQuery.page;
  delete filterQuery.limit;
  delete filterQuery.sortBy;
  delete filterQuery.sortOrder;
  delete filterQuery.role;

  // Validate pagination parameters
  const currentPage = Math.max(1, page);
  const itemsPerPage = Math.min(Math.max(1, limit), 100); // Max 100 items per page
  const skip = (currentPage - 1) * itemsPerPage;

  // Build sort object
  const sortObject: Record<string, 1 | -1> = {};
  sortObject[sortBy] = sortOrder;

  // Build aggregation pipeline for role filtering
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const pipeline: any[] = [
    // Match transaction-specific filters first
    ...(Object.keys(filterQuery).length > 0 ? [{ $match: filterQuery }] : []),

    // Lookup sender data
    {
      $lookup: {
        from: 'users',
        localField: 'sender',
        foreignField: '_id',
        as: 'senderData'
      }
    },

    // Lookup receiver data
    {
      $lookup: {
        from: 'users',
        localField: 'receiver',
        foreignField: '_id',
        as: 'receiverData'
      }
    },

    // Filter by role if specified (transactions where sender OR receiver has the specified role)
    ...(role ? [{
      $match: {
        $or: [
          { 'senderData.role': role },
          { 'receiverData.role': role }
        ]
      }
    }] : []),

    // Project the final structure
    {
      $project: {
        sender: {
          $cond: {
            if: { $gt: [{ $size: '$senderData' }, 0] },
            then: {
              _id: { $arrayElemAt: ['$senderData._id', 0] },
              name: { $arrayElemAt: ['$senderData.name', 0] },
              email: { $arrayElemAt: ['$senderData.email', 0] }
            },
            else: null
          }
        },
        receiver: {
          $cond: {
            if: { $gt: [{ $size: '$receiverData' }, 0] },
            then: {
              _id: { $arrayElemAt: ['$receiverData._id', 0] },
              name: { $arrayElemAt: ['$receiverData.name', 0] },
              email: { $arrayElemAt: ['$receiverData.email', 0] }
            },
            else: null
          }
        },
        amount: 1,
        type: 1,
        status: 1,
        fee: 1,
        commission: 1,
        description: 1,
        createdAt: 1,
        updatedAt: 1
      }
    }
  ];

  // Get total count with the same filters
  const countPipeline = [...pipeline, { $count: 'total' }];
  const countResult = await Transaction.aggregate(countPipeline);
  const totalItems = countResult.length > 0 ? countResult[0].total : 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Add sorting, skip, and limit to the main pipeline
  pipeline.push(
    { $sort: sortObject },
    { $skip: skip },
    { $limit: itemsPerPage }
  );

  const transactions = await Transaction.aggregate(pipeline);

  return {
    data: transactions,
    pagination: {
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    },
  };
};

export const TransactionService = {
  getMyTransactions,
  getAllTransactions,
};