import { Transaction, ITransaction } from './transaction.model';

interface PaginationQuery {
  page?: string;
  limit?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface PaginatedResult<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

const getMyTransactions = async (userId: string, query: PaginationQuery = {}): Promise<PaginatedResult<ITransaction>> => {
  // Extract pagination parameters with defaults
  const page = parseInt(query.page || '1', 10);
  const limit = parseInt(query.limit || '10', 10);
  const sortBy = query.sortBy || 'createdAt';
  const sortOrder = query.sortOrder === 'asc' ? 1 : -1;

  // Validate pagination parameters
  const currentPage = Math.max(1, page);
  const itemsPerPage = Math.min(Math.max(1, limit), 100); // Max 100 items per page
  const skip = (currentPage - 1) * itemsPerPage;

  // Build sort object
  const sortObject: Record<string, 1 | -1> = {};
  sortObject[sortBy] = sortOrder;

  // Build query for user's transactions
  const transactionQuery = {
    $or: [{ sender: userId }, { receiver: userId }],
  };

  // Get total count for pagination
  const totalItems = await Transaction.countDocuments(transactionQuery);
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Get paginated transactions
  const transactions = await Transaction.find(transactionQuery)
    .populate('sender receiver', 'name email')
    .sort(sortObject)
    .skip(skip)
    .limit(itemsPerPage);

  return {
    data: transactions,
    pagination: {
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    },
  };
};

const getAllTransactions = async (query: Record<string, unknown>) => {
  const transactions = await Transaction.find(query).populate('sender receiver').sort({ createdAt: -1 });
  return transactions;
};

export const TransactionService = {
  getMyTransactions,
  getAllTransactions,
};